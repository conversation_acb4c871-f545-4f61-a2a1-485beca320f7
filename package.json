{"name": "clinico-sg-odoo-gateway", "version": "1.0.0", "main": "index.js", "scripts": {"clean": "npx rimraf ./dist", "start": "node dist/index.js", "start:dev": "NODE_ENV=local npm run clean && npx tsc-watch -p tsconfig.json --onSuccess \"node dist/index.js\"", "build": "npm run clean && npx tsc && cp .env* ./dist", "dev": "ts-node src/index.ts", "test": "jest"}, "_moduleAliases": {"@": "./dist"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "fastify": "^5.4.0", "fastify-plugin": "^5.0.1", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "pino": "^9.8.0", "pino-pretty": "^13.1.1"}, "devDependencies": {"@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.2.1", "@types/supertest": "^6.0.3", "jest": "^30.0.5", "nock": "^14.0.9", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}