FROM node:20.10.0-alpine3.19 AS builder

WORKDIR /build
COPY . .

RUN npm ci \
    && npm run build \
    && rm -rf node_modules \
    && npm ci --only=production


FROM node:20.10.0-alpine3.19

RUN apk update \
    && apk upgrade \
    && apk add tzdata

RUN cp /usr/share/zoneinfo/Asia/Taipei /etc/localtime
RUN echo "Asia/Taipei" >  /etc/timezone

WORKDIR /usr/src/app
COPY --from=builder /build/dist ./dist
COPY --from=builder /build/node_modules ./node_modules
# copy env files, last arg is destination
COPY [ "package.json", ".env", "./" ]

CMD [ "node", "dist/app.js" ]
