
import { FastifyInstance } from 'fastify';
import jwt from 'jsonwebtoken';
import { build } from '../src/app';

const JWT_SECRET = process.env.JWT_SECRET || 'a-very-secret-key';

describe('/api/v1/auth/status', () => {
  let server: FastifyInstance;
  let validToken: string;
  const userInfo = { username: 'testuser', odooSessionId: 'some-odoo-session' };

  beforeAll(async () => {
    server = build();
    await server.ready();
    validToken = jwt.sign(userInfo, JWT_SECRET, { expiresIn: '1h' });
  });

  afterAll(async () => {
    await server.close();
  });

  it('should return 200 if token is valid and session exists', async () => {
    // Mock Redis response
    await server.redis.set(`odoo:session:${userInfo.odooSessionId}`, 'exists', 'EX', 60);

    const response = await server.inject({
      method: 'GET',
      url: '/api/v1/auth/status',
      headers: {
        authorization: `Bearer ${validToken}`,
      },
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload.status).toBe('ok');
    expect(payload.username).toBe(userInfo.username);

    // Clean up redis
    await server.redis.del(`odoo:session:${userInfo.odooSessionId}`);
  });

  it('should return 401 if session does not exist in Redis', async () => {
    const response = await server.inject({
      method: 'GET',
      url: '/api/v1/auth/status',
      headers: {
        authorization: `Bearer ${validToken}`,
      },
    });

    expect(response.statusCode).toBe(401);
    const payload = JSON.parse(response.payload);
    expect(payload.code).toBe('SessionExpired');
  });

  it('should return 401 if token is invalid', async () => {
    const response = await server.inject({
      method: 'GET',
      url: '/api/v1/auth/status',
      headers: {
        authorization: 'Bearer invalid-token',
      },
    });

    expect(response.statusCode).toBe(401);
    const payload = JSON.parse(response.payload);
    expect(payload.code).toBe('Unauthorized');
  });

  it('should return 401 if token is not provided', async () => {
    const response = await server.inject({
      method: 'GET',
      url: '/api/v1/auth/status',
    });

    expect(response.statusCode).toBe(401);
  });
});
