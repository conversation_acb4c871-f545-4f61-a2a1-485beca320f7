
import { FastifyInstance } from 'fastify';
import nock from 'nock';
import { build } from '../src/app';

const ODOO_URL = process.env.ODOO_URL || 'http://localhost:8069';

describe('/api/v1/auth/login', () => {
  let server: FastifyInstance;

  beforeAll(async () => {
    server = build();
    await server.ready();
  });

  afterAll(async () => {
    await server.close();
  });

  afterEach(() => {
    nock.cleanAll();
  });

  it('should return a JWT token on successful login', async () => {
    // Mock Odoo's response
    nock(ODOO_URL)
      .post('/web/session/authenticate')
      .reply(200, 
        { jsonrpc: '2.0', id: null, result: { uid: 1 } },
        { 'Set-Cookie': 'session_id=some-fake-session-id;' }
      );

    const response = await server.inject({
      method: 'POST',
      url: '/api/v1/auth/login',
      payload: {
        username: 'testuser',
        password: 'password',
      },
    });

    expect(response.statusCode).toBe(200);
    const payload = JSON.parse(response.payload);
    expect(payload).toHaveProperty('token');
    expect(payload.message).toBe('登入成功');
  });

  it('should return 401 on failed login', async () => {
    // Mock Odoo's response for failed auth
    nock(ODOO_URL)
      .post('/web/session/authenticate')
      .reply(200, { 
        jsonrpc: '2.0', 
        id: null, 
        error: { code: 100, message: 'Authentication failed.' } 
      });

    const response = await server.inject({
      method: 'POST',
      url: '/api/v1/auth/login',
      payload: {
        username: 'wronguser',
        password: 'wrongpassword',
      },
    });

    expect(response.statusCode).toBe(401);
    const payload = JSON.parse(response.payload);
    expect(payload.code).toBe('AuthFailed');
  });

  it('should return 401 if Odoo API call fails', async () => {
    // Mock Odoo's response for a network error
    nock(ODOO_URL)
      .post('/web/session/authenticate')
      .reply(500);

    const response = await server.inject({
      method: 'POST',
      url: '/api/v1/auth/login',
      payload: {
        username: 'testuser',
        password: 'password',
      },
    });

    expect(response.statusCode).toBe(401);
    const payload = JSON.parse(response.payload);
    expect(payload.code).toBe('AuthFailed');
  });
});
