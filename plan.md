# Odoo 後端中介 API 專案基礎建設規劃（初期驗證版）

## 1. 專案目標
建立以 Fastify + TypeScript 為核心的中介 API，  
**初期重點為實作與 Odoo 的登入驗證（session authenticate）流程，**  
確保後續可延伸其他 API 功能。

---

## 2. 初期核心功能
- 與 Odoo `/web/session/authenticate` 介面進行帳號密碼登入
- 提供 API 端點以驗證登入狀態
- 確保連線錯誤、驗證失敗等錯誤處理完整

---

## 3. 技術選型與架構
- Node.js + TypeScript + Fastify
- axios 作為 HTTP 客戶端呼叫 Odoo
- pino 日誌
- Jest + Supertest 測試架構

---

## 4. 初期 API 規劃

### POST /api/v1/auth/login
- 功能：接收使用者帳密，呼叫 Odoo 驗證，回傳登入結果與 token（可為中介 API 自行 JWT）
- Request Body：
  ```json
  {
    "username": "odoo_user",
    "password": "password"
  }
  ```
- Response：

成功：
```json
{
  "message": "登入成功",
  "token": "jwt-token",
  "expiresIn": 3600
}
```

失敗：
```json
{
  "code": "AuthFailed",
  "message": "帳號密碼錯誤"
}
```

## 5. 認證流程
- 前端向中介 API `/auth/login` 傳入帳號與密碼
- 中介 API 呼叫 Odoo `/web/session/authenticate` 進行登入驗證
- 驗證成功後，中介 API 產生並回傳 JWT token，供前端後續 API 呼叫使用
- 後續 API 呼叫需攜帶 JWT，Fastify 中介軟體負責驗證 JWT

---

## 6. 開發流程與 TDD
- 先撰寫登入相關 API 的單元測試與整合測試，包括：
  - 登入成功流程測試
  - 登入失敗（帳密錯誤）測試
  - Odoo API 呼叫異常測試
- 使用 `jest` 搭配 `nock` 模擬 Odoo API 回應，避免開發與測試過程直接呼叫正式環境
- 測試先行（紅燈階段）：撰寫測試描述需求行為
- 功能實作（綠燈階段）：寫足以通過測試的最小程式碼
- 重構階段：優化程式碼結構與可讀性，確保測試持續通過

---

## 7. Swagger 文件
- 利用 `fastify-swagger` 插件生成 OpenAPI 3.0 文件
- 文件涵蓋初期 `/auth/login` 與 `/auth/status` 兩個 API
- 提供 Swagger UI 介面，預設路徑為 `/docs`
- 文件應隨 API 變更同步更新，並納入 CI/CD 驗證流程

---

## 8. 初期開發任務清單
1. 初始化 Fastify + TypeScript 專案架構及開發環境設定
2. 撰寫 Odoo 認證（`/web/session/authenticate`）呼叫函式
3. 開發 `/auth/login` API 路由與 JWT 產生邏輯
4. 開發 `/auth/status` API 路由，驗證 JWT 有效性
5. 撰寫對應單元測試與整合測試，使用 `nock` 模擬 Odoo 回應
6. 整合並啟用 Swagger 文件功能
7. 編寫 Dockerfile 並設置基本 CI/CD 流程（含測試階段）
