stages:
  - deploy

deploy:
  stage: deploy
  variables:
    EEV_UPSTREAM_PROJECT_PATH: $CI_PROJECT_PATH
    EEV_UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    EEV_UPSTREAM_BRANCH: $CI_COMMIT_BRANCH
  rules:
    # prod with tag
    - if: $CI_COMMIT_TAG
      variables:
        EEV_UPSTREAM_TAG: $CI_COMMIT_TAG
        EEV_UPSTREAM_BRANCH: $CI_DEFAULT_BRANCH
    # dev(dev branch)
    - if: $CI_COMMIT_BRANCH == 'dev'
      variables:
        EEV_UPSTREAM_TAG: $CI_COMMIT_BRANCH
    # manual
    - if: $EEV_UPSTREAM_TAG
      variables:
        EEV_UPSTREAM_TAG: $EEV_UPSTREAM_TAG
  trigger:
    project: devops/helm-charts/gateway/clinico-sg-odoo-gateway
    branch: master
    strategy: depend
