import { authenticateOdoo } from '@/services/odoo';
import { FastifyPluginAsync } from 'fastify';

import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'a-very-secret-key';

const loginRoute: FastifyPluginAsync = async (fastify) => {
  fastify.post(
    '/login',
    {
      schema: {
        body: {
          type: 'object',
          required: ['username', 'password'],
          properties: {
            username: { type: 'string' },
            password: { type: 'string' },
          },
        },
        response: {
          200: {
            type: 'object',
            properties: {
              message: { type: 'string' },
              token: { type: 'string' },
              expiresIn: { type: 'number' },
            },
          },
          401: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
            },
          },
        },
      },
    },
    async (request, reply) => {
      const { username, password } = request.body as any;

      const odooSessionId = await authenticateOdoo(fastify, username, password);

      if (!odooSessionId) {
        reply.code(401);
        return {
          code: 'AuthFailed',
          message: '帳號密碼錯誤或 Odoo 連線失敗',
        };
      }

      // If Odoo auth is successful, create a JWT
      const expiresIn = 3600; // 1 hour
      const token = jwt.sign({ username, odooSessionId }, JWT_SECRET, {
        expiresIn,
      });

      return {
        message: '登入成功',
        token,
        expiresIn,
      };
    },
  );
};

export default loginRoute;
