import { FastifyPluginAsync } from 'fastify';

const statusRoute: FastifyPluginAsync = async (fastify) => {
  fastify.get(
    '/status',
    {
      // This schema can be expanded later
      schema: {
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              username: { type: 'string' },
            },
          },
          401: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
            },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request) => {
      // The user object will be attached by the JWT verification hook
      const user = (request as any).user;

      return {
        status: 'ok',
        username: user.username,
      };
    },
  );
};

export default statusRoute;
