
import fp from 'fastify-plugin';
import Redis from 'ioredis';
import { FastifyInstance } from 'fastify';

declare module 'fastify' {
  interface FastifyInstance {
    redis: Redis;
  }
}

async function redisPlugin(fastify: FastifyInstance, options: any) {
  const redis = new Redis({
    // Basic connection options, can be extended via environment variables
    host: process.env.REDIS_HOST || '127.0.0.1',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
  });

  redis.on('error', (err) => {
    fastify.log.error({ err }, 'Redis connection error');
  });

  fastify.decorate('redis', redis);

  fastify.addHook('onClose', (instance: FastifyInstance, done: (err?: Error) => void) => {
    instance.redis.quit(() => done());
  });
}

export default fp(redisPlugin, { name: 'redis' });
