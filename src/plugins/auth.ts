
import fp from 'fastify-plugin';
import jwt from 'jsonwebtoken';
import { FastifyRequest, FastifyReply } from 'fastify';

const JWT_SECRET = process.env.JWT_SECRET || 'a-very-secret-key';

declare module 'fastify' {
  interface FastifyInstance {
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
  interface FastifyRequest {
    user: any; // Or a more specific type for your user payload
  }
}

async function authPlugin(fastify: any, options: any) {
  fastify.decorate('authenticate', async function (request: FastifyRequest, reply: FastifyReply) {
    try {
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        reply.code(401).send({ code: 'Unauthorized', message: 'Missing or invalid authorization header' });
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer '
      const decoded = jwt.verify(token, JWT_SECRET);
      request.user = decoded;

    } catch (err) {
      reply.code(401).send({ code: 'Unauthorized', message: 'Invalid token' });
    }
  });
}

export default fp(authPlugin, { name: 'auth' });
