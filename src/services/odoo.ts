
import axios from 'axios';
import { FastifyInstance } from 'fastify';

// A placeholder for Odoo configuration
const ODOO_URL = process.env.ODOO_URL || 'http://localhost:8069';

/**
 * Authenticates with the Odoo JSON-RPC endpoint.
 * @param fastify - The Fastify instance, used for logging.
 * @param username - The Odoo username.
 * @param password - The Odoo password.
 * @returns The session ID from Odoo if authentication is successful, null otherwise.
 */
export async function authenticateOdoo(fastify: FastifyInstance, username: string, password: string): Promise<string | null> {
  const url = `${ODOO_URL}/web/session/authenticate`;
  const params = {
    jsonrpc: '2.0',
    method: 'call',
    params: {
      db: process.env.ODOO_DB || 'odoo',
      login: username,
      password: password,
      context: {},
    },
  };

  try {
    fastify.log.info(`Authenticating user: ${username} at ${url}`);
    const response = await axios.post(url, params, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data.error) {
      fastify.log.error({ error: response.data.error }, 'Odoo authentication failed');
      return null;
    }

    const setCookieHeader = response.headers['set-cookie'];
    const cookies = Array.isArray(setCookieHeader) ? setCookieHeader : (setCookieHeader ? [setCookieHeader] : []);

    if (cookies.length === 0) {
        fastify.log.error("No Set-Cookie header found in Odoo's response or it's empty");
        return null;
    }

    const sessionIdCookieFound: string | undefined = cookies.find(cookie => cookie.startsWith('session_id='));
    if (sessionIdCookieFound) {
        // @ts-ignore
        const sessionIdParts = sessionIdCookieFound.split(';')[0].split('=');
        if (sessionIdParts.length < 2 || !sessionIdParts[1]) {
            fastify.log.error("Could not parse session_id from cookie");
            return null;
        }
        const sessionId = sessionIdParts[1];

        fastify.log.info(`Successfully authenticated user ${username}.`);

        return sessionId;
    } else {
        fastify.log.error("session_id cookie not found in Odoo's response");
        return null;
    }

  } catch (error) {
    fastify.log.error({ err: error }, 'Error calling Odoo authentication API');
    return null;
  }
}
