
import Fastify from 'fastify';
import redisPlugin from './plugins/redis';
import authPlugin from './plugins/auth';
import loginRoute from './routes/api/v1/auth/login';
import statusRoute from './routes/api/v1/auth/status';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';

export function build() {
  const server = Fastify({
    logger: {
      transport: {
        target: 'pino-pretty',
      },
    },
  });

  // Register Swagger
  server.register(swagger, {
    swagger: {
      info: {
        title: 'Odoo Gateway API',
        description: 'API documentation for the Odoo Gateway',
        version: '0.1.0'
      },
      externalDocs: {
        url: 'https://swagger.io',
        description: 'Find more info here'
      },
      host: 'localhost:3000',
      schemes: ['http'],
      consumes: ['application/json'],
      produces: ['application/json']
    }
  });

  server.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'full',
      deepLinking: false
    },
  });

  // Register plugins
  server.register(redisPlugin);
  server.register(authPlugin);

  // Register routes
  server.register(loginRoute, { prefix: '/api/v1/auth' });
  server.register(statusRoute, { prefix: '/api/v1/auth' });

  server.get('/', async (request, reply) => {
    return { hello: 'world' };
  });

  return server;
}
